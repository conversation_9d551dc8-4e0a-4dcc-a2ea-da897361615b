name: flutter_mecfuture_2_0
description: "A new Flutter project - MEC Future 2.0."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

version: 1.0.7+7

environment:
  sdk: '>=3.3.0 <4.0.0'

dependencies:
  accordion: ^2.6.0
  android_play_install_referrer: ^0.4.0
  anim_search_bar: ^2.0.3
  app_links: ^6.4.0
  app_tracking_transparency: ^2.0.4
  audio_waveforms: ^1.0.5
  audioplayers: ^6.0.0
  badges: ^3.1.2
  bloc: ^9.0.0
  bloc_concurrency: ^0.3.0
  buttons_tabbar: ^1.3.15
  cached_network_image: ^3.3.1
  camera: ^0.11.0+1
  card_swiper: ^3.0.1
  flutter_swiper_null_safety: ^1.0.2
  carousel_slider: ^5.1.1
  change_app_package_name: ^1.1.0
  chewie: ^1.8.1
  countdown_progress_indicator: ^0.1.3
  crypto: ^3.0.3
  cupertino_icons: ^1.0.8
  data_table_2: ^2.5.11
  device_info_plus: ^11.5.0
  devicelocale: ^0.8.1
  dio: ^5.4.3
  dots_indicator: ^4.0.1
  dotted_border: ^3.1.0
  dropdown_button2: ^2.3.9
  easy_localization: ^3.0.5
  encrypt: ^5.0.3
  entry: ^1.0.1
  expandable: ^5.0.1
  feature_discovery: ^0.14.2
  file_picker: ^9.2.3
  firebase_analytics: ^11.1.0
  firebase_core: ^3.1.1
  
  firebase_messaging: ^15.0.1
  firebase_remote_config: ^5.4.5
  fl_chart: ^1.0.0
  flick_video_player: ^0.9.0
  flutter:
    sdk: flutter
  flutter_bloc: ^9.1.1
  flutter_cached_pdfview: ^0.4.2
  flutter_countdown_timer: ^4.1.0
  flutter_downloader: ^1.11.7
  flutter_html: ^3.0.0
  flutter_html_table: ^3.0.0
  flutter_inappwebview: ^6.0.0
  flutter_isolate: ^2.0.4
  flutter_linkify: ^6.0.0
  flutter_local_notifications: ^19.3.0
  flutter_pdfview: ^1.3.2
  flutter_svg: ^2.0.10+1
  flutter_timezone: ^4.1.1
  flutter_widget_from_html: ^0.16.0
  geocoding: ^4.0.0
  get: ^4.6.6
  google_fonts: ^6.2.1
  grouped_list: ^6.0.0
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  html_editor_enhanced: ^2.7.0
  html_unescape: ^2.0.0
  http: ^1.2.1
  image_cropper: ^9.1.0
  image_picker: ^1.1.1
  injector: ^4.0.0
  
  internet_connection_checker: ^3.0.1
  intl: ^0.20.2
  intl_phone_field: ^3.2.0
  location: ^8.0.1
  lottie: ^3.1.2
  marquee: ^2.2.3
  material_dialogs: ^1.1.4
  material_floating_search_bar_2: ^0.5.0
  open_filex: ^4.4.0
  otp_autofill: ^4.1.0
  package_info_plus: ^8.0.0
  page_transition: ^2.1.0
  path_provider: ^2.1.3
  percent_indicator: ^4.2.3
  permission_handler: ^11.3.1
  photo_view: ^0.15.0
  pinput: ^5.0.1
  provider: ^6.1.2
  pull_to_refresh: ^2.0.0
  mobile_scanner: ^7.0.1
  record: ^6.0.0
  share_plus: ^11.0.0
  shared_preferences: ^2.2.3
  shimmer: ^3.0.0
  simple_barcode_scanner: ^0.3.0
  simple_gradient_text: ^1.3.0
  smooth_video_progress: ^0.0.4
  speech_to_text: ^7.1.0
  stop_watch_timer: ^3.1.0
  syncfusion_flutter_charts: ^30.1.39
  timezone: ^0.10.1
  # uni_links: ^0.5.1  # Replaced with app_links
  url_launcher: ^6.3.0
  video_compress: ^3.1.2
  video_player: ^2.8.6
  # video_thumbnail: ^0.5.3
  
  video_trimmer: ^5.0.0
  visibility_detector: ^0.4.0+2
  webview_flutter: ^4.8.0
  webview_flutter_android: ^4.7.0
  youtube_player_flutter: ^9.0.1
  zoom_pinch_overlay: ^1.4.3
  # install_referrer: ^1.3.0  # Temporarily disabled due to namespace issue
  firebase_dynamic_links: ^6.1.8
  equatable: ^2.0.7
  path: ^1.9.1
  flutter_cache_manager: ^3.4.1
  marqueer: ^2.1.0
  # qr_code_scanner: ^1.0.1  # Temporarily disabled due to namespace issue

dependency_overrides: 
  video_thumbnail: ^0.5.3

dev_dependencies:
  dependency_validator: ^5.0.2
  flutter_lints: ^6.0.0
  flutter_test:
    sdk: flutter

# The following section is specific to Flutter packages.
flutter:
  uses-material-design: true

  assets:
    - assets/images/
    - assets/images/ebook/
    - assets/avatar/
    - assets/images/splash/
    - assets/images/preboarding/
    - assets/policy.html
    - assets/terms.html
    - assets/images/temp/
    - assets/translations/ar.json
    - assets/translations/en.json
    - assets/translations/hi.json
    - assets/translations/mr.json
    - assets/translations/ta.json
    - assets/translations/or.json
    - assets/dialog_flow_auth.json
    - assets/cong_example.json
    - assets/clickevent.html

  fonts:
    - family: OpenSansBold
      fonts:
        - asset: assets/fonts/OpenSans-Bold.ttf
    - family: OpenSans_SemiBold
      fonts:
        - asset: assets/fonts/OpenSans-SemiBold.ttf
    - family: OpenSans_Regular
      fonts:
        - asset: assets/fonts/OpenSans-Regular.ttf
    - family: CairoBold
      fonts:
        - asset: assets/fonts/Cairo-Bold.ttf
    - family: Cairo_SemiBold
      fonts:
        - asset: assets/fonts/Cairo-SemiBold.ttf
    - family: Cairo_Regular
      fonts:
        - asset: assets/fonts/Cairo-Regular.ttf
    - family: DMSans_Bold
      fonts:
        - asset: assets/fonts/DMSans-Bold.ttf
    - family: DMSans_Regular
      fonts:
        - asset: assets/fonts/DMSans-Regular.ttf
